<?php if($errors->any()): ?>
    <?php if (isset($component)) { $__componentOriginalcc4d6dcf44f2ce2176eee5939e2828e6 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalcc4d6dcf44f2ce2176eee5939e2828e6 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.card.body','data' => ['borderTop' => true]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('card.body'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['borderTop' => true]); ?>
        <?php if (isset($component)) { $__componentOriginalb2cd6c3734aae9f8ed791b2c94d2d3ba = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalb2cd6c3734aae9f8ed791b2c94d2d3ba = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.form.helper','data' => ['helper' => 'Errors']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('form.helper'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['helper' => 'Errors']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalb2cd6c3734aae9f8ed791b2c94d2d3ba)): ?>
<?php $attributes = $__attributesOriginalb2cd6c3734aae9f8ed791b2c94d2d3ba; ?>
<?php unset($__attributesOriginalb2cd6c3734aae9f8ed791b2c94d2d3ba); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalb2cd6c3734aae9f8ed791b2c94d2d3ba)): ?>
<?php $component = $__componentOriginalb2cd6c3734aae9f8ed791b2c94d2d3ba; ?>
<?php unset($__componentOriginalb2cd6c3734aae9f8ed791b2c94d2d3ba); ?>
<?php endif; ?>
        <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <?php if (isset($component)) { $__componentOriginal3eab721d96f8288933fe59df3467ab61 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal3eab721d96f8288933fe59df3467ab61 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.text.error','data' => ['error' => ''.e($error).'']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('text.error'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['error' => ''.e($error).'']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal3eab721d96f8288933fe59df3467ab61)): ?>
<?php $attributes = $__attributesOriginal3eab721d96f8288933fe59df3467ab61; ?>
<?php unset($__attributesOriginal3eab721d96f8288933fe59df3467ab61); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal3eab721d96f8288933fe59df3467ab61)): ?>
<?php $component = $__componentOriginal3eab721d96f8288933fe59df3467ab61; ?>
<?php unset($__componentOriginal3eab721d96f8288933fe59df3467ab61); ?>
<?php endif; ?>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalcc4d6dcf44f2ce2176eee5939e2828e6)): ?>
<?php $attributes = $__attributesOriginalcc4d6dcf44f2ce2176eee5939e2828e6; ?>
<?php unset($__attributesOriginalcc4d6dcf44f2ce2176eee5939e2828e6); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalcc4d6dcf44f2ce2176eee5939e2828e6)): ?>
<?php $component = $__componentOriginalcc4d6dcf44f2ce2176eee5939e2828e6; ?>
<?php unset($__componentOriginalcc4d6dcf44f2ce2176eee5939e2828e6); ?>
<?php endif; ?>
<?php endif; ?><?php /**PATH C:\KodeCreators\newlife-panel\resources\views/components/group/errors.blade.php ENDPATH**/ ?>