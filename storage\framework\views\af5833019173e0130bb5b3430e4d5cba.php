

<?php $__env->startSection('content'); ?>
    <div class="card card-custom mb-5">

        <div class="card-body" x-data="{ showFilter: false }">

            <div class="row justify-content-between ">
                <div class="col-12 col-sm-8 col-md-6 col-lg-4 col-xl-3 mb-6">
                    <div class="input-icon">
                        <input type="text" class="form-control" placeholder="Search..." id="users_search" />
                        <span>
                            <i class="flaticon2-search-1 text-muted"></i>
                        </span>
                    </div>

                </div>
            </div>

            <div class="datatable datatable-bordered datatable-head-custom" id="users_dt"></div>
        </div>
    </div>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('styles'); ?>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('scripts'); ?>
    <script src="https://cdn.jsdelivr.net/gh/alpinejs/alpine@v2.7.0/dist/alpine.min.js" defer></script>
    <script>
        var datatable;
        var datatableElement;
        var searchElement;
        var columnArray;

        const storagePath = `<?php echo e(url('/storage')); ?>`;
        const apiRoute = `<?php echo e(route('archive.api')); ?>`;
        let url = "<?php echo e(Storage::url('/')); ?>";
        const deleteRoute = `<?php echo e(route('users.delete', ['::ID'])); ?>`;
        const viewRoute = `<?php echo e(route('archive.show-all-pdf', ['importId' => '::ID'])); ?>`;
        const downloadRoute = `<?php echo e(route('archive.download-all-pdf', ['importId' => '::ID'])); ?>`;


        datatableElement = $('#users_dt');
        searchElement = $('#users_search');

        columnArray = [{
                field: 'file_name',
                title: `File Name`,
                width: 400,
                sortable: true,
                autoHide: false,
            },
            {
                field: 'created_at',
                title: `Created At`,
                width: 200,
                sortable: true,
                autoHide: false,
                template: function(data) {
                    return data.created_at ? moment(data.created_at).format('MM/DD/YYYY hh:mm A') : '';
                }
            },
            {
                field: 'Actions',
                title: 'Actions',
                sortable: false,
                width: 'auto',
                overflow: 'visible',
                autoHide: false,
                template: function(data) {
                    return `
                            <a data-id="${data.id}" class="btn btn-sm btn-clean btn-icon" id="download-all-btn" data-toggle="tooltip" title="Download All Scripts">
                                <i class="menu-icon fas fa-download"></i>
                            </a>
                            <a href="${viewRoute.replace('::ID', data.id)}" class="btn btn-sm btn-clean btn-icon" data-toggle="tooltip" title="View Scripts">
                                <i class="menu-icon fas fa-eye"></i>
                            </a>
                            `;
                },
            }
        ];

        datatable = datatableElement.KTDatatable({
            data: {
                type: 'remote',
                source: {
                    read: {
                        url: apiRoute,
                        //sample custom headers
                        headers: {
                            'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>'
                        },
                        map: function(raw) {
                            // sample data mapping
                            var dataSet = raw;
                            if (typeof raw.data !== 'undefined') {
                                dataSet = raw.data;
                            }
                            return dataSet;
                        },
                    },
                },
                pageSize: 10,
                serverPaging: true,
                serverFiltering: true,
                serverSorting: true,
            },
            pagination: true,
            search: {
                input: searchElement,
                key: 'search'
            },
            layout: {
                customScrollbar: false,
                scroll: true,
            },
            columns: columnArray
        });

        const routeTemplate = "<?php echo e(route('archive.download-all-pdf', ['importId' => '__ID__'])); ?>";

        // Handle download all button
        datatableElement.on('click', '#download-all-btn', function() {
            const importId = $(this).data('id');
            const url = routeTemplate.replace('__ID__', importId ?? '');

            const form = $('<form>', {
                method: 'POST',
                action: url
            });

            // Add CSRF token
            form.append($('<input>', {
                type: 'hidden',
                name: '_token',
                value: '<?php echo e(csrf_token()); ?>'
            }));

            // Submit the form
            $('body').append(form);
            form.submit();
            form.remove();
        });
    </script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\KodeCreators\newlife-panel\resources\views/archive/index.blade.php ENDPATH**/ ?>