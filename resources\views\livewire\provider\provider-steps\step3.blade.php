<div>
    <x-form.input.text label="Phone" labelRequired="0" model="user.phone" type="number" />
    <x-form.input.text label="Fax" labelRequired="0" model="user.fax" type="number" />
    <x-form.input.drop-down label="Default Dispatch Method" labelRequired="1" model="user.default_dispatch_method">
        <option value="">Select Method</option>
        <option value="{{ User::DISPATCH_METHOD_FAX }}"
            {{ $user->default_dispatch_method == User::DISPATCH_METHOD_FAX ? 'selected' : '' }}>
            Fax Plus
        </option>
        <option value="{{ User::DISPATCH_METHOD_DISPENSE_PRO }}"
            {{ $user->default_dispatch_method == User::DISPATCH_METHOD_DISPENSE_PRO ? 'selected' : '' }}>
            Dispense Pro</option>
    </x-form.input.drop-down>
    @if ($user['default_dispatch_method'] === User::DISPATCH_METHOD_DISPENSE_PRO)
        <div class="col-md-12 mb-3">
            <x-form.input.text label="DispensePro abbreviation" labelRequired="1" model="user.dispense_abbreviation" />
        </div>
    @endif
    <x-form.input.text label="Address" labelRequired="0" model="user.address" />
    <x-form.input.text label="City" labelRequired="0" model="user.city" />
    <x-form.input.drop-down label="State" labelRequired="0" model="user.state_id" placeholder="Select State">
        <option value="">Select State</option>
        @foreach ($states as $state)
            <option value="{{ $state->id }}" {{ $user->state_id == $state->id ? 'selected' : '' }}>
                {{ $state->name }}
            </option>
        @endforeach
    </x-form.input.drop-down>
    <x-form.input.text label="Zip" labelRequired="0" model="user.zip" />

</div>
