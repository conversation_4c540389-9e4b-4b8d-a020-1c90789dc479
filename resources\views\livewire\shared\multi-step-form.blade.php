{{-- Progress Steps --}}
<div class="flex justify-center space-x-6 mb-6">
    @foreach ($steps as $index => $step)
        <div class="text-center">
            <div class="w-10 h-10 rounded-full flex items-center justify-center {{ $currentStep === $index ? 'bg-purple-200' : 'bg-gray-300' }}">
                {{ $index + 1 }}
            </div>
            <div class="mt-2 text-sm">{{ $step['label'] }}</div>
        </div>
    @endforeach
</div>

{{-- Current Step View --}}
<div>
    @include($steps[$currentStep]['view'], ['formData' => &$formData])
</div>

{{-- Navigation Buttons --}}
<div class="mt-6 flex justify-between">
    @if ($currentStep > 0)
        <button wire:click="back" class="px-4 py-2 bg-gray-400 text-white rounded">Back</button>
    @endif

    @if ($currentStep < count($steps) - 1)
        <button wire:click="next" class="px-4 py-2 bg-purple-500 text-white rounded">Next</button>
    @endif
</div>
