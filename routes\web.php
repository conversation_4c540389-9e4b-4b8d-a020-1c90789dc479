<?php

use App\Http\Controllers\ExcelImportController;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\ExcelToPdfController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

//this route is just for a quickly test anything, please ignore it
Route::get('/test', 'App\Http\Controllers\TestController@test');
Route::get('/test-merge', 'App\Http\Controllers\TestController@mergePdf');



Route::get('/home', function () {
    return redirect('/dashboard');
});
Route::get('/', function () {
    return redirect('/dashboard');
});

Auth::routes();

Route::get('/forgot-password', function () {
    return view('auth.forgot');
})->name('forgot-password');

Route::post('/forgot-password', 'App\Http\Controllers\Admin\HomeController@forgotSendEmail')->name('forgot-password-post');

// Apply SetLocale middleware globally to all web routes
Route::group(['middleware' => ['setlocale', 'auth', 'force.password.change'], 'namespace' => 'App\\Http\\Controllers'], function () {


    Route::post('/store-device-time', [ExcelImportController::class, 'storeDeviceTime'])->name('store-device-time');

    Route::get('/new-import', [ExcelImportController::class, 'newImport'])->name('excel.new-import');
    Route::get('/view-pdf/{id}', [ExcelImportController::class, 'viewPdf'])->name('excel.view-pdf');


    // Excel to PDF conversion routes
    Route::get('/excel-import', [ExcelImportController::class, 'import'])->name('excel.import')->middleware('provider');
    Route::post('/excel-import', [ExcelImportController::class, 'store'])->name('excel.store');
    Route::post('/excel-import/update-preview-data', [ExcelImportController::class, 'updatePreviewData'])->name('excel.update-preview-data');
    Route::post('/excel-import/process', [ExcelImportController::class, 'process'])->name('excel.process');
    Route::get('/excel-import/download-pdf/{id?}', [ExcelImportController::class, 'downloadPdf'])->name('excel.download-pdf');
    Route::post('/excel-import/download-selected-pdf', [ExcelImportController::class, 'downloadSelectedPdf'])->name('excel.download-selected-pdf');
    Route::match(['get', 'post'], '/excel-import/download-all-pdf/{importId?}', [ExcelImportController::class, 'downloadAllPdf'])->name('excel.download-all-pdf');
    Route::post('/prescription/update-status', [ExcelImportController::class, 'updateStatus'])->name('prescription.update-status');
    Route::post('/prescription/get-import-id', [ExcelImportController::class, 'getImportId'])->name('prescription.get-import-id');
    Route::get('/excel-import/signed/{importId?}', [ExcelImportController::class, 'viewSigned'])->name('excel.view-signed');
    Route::get('/excel-import/sent/{importId?}', [ExcelImportController::class, 'viewSent'])->name('excel.view-sent');
    Route::get('/excel-to-pdf', [ExcelToPdfController::class, 'index'])->name('excel-to-pdf.index');
    Route::post('/excel-to-pdf/convert', [ExcelToPdfController::class, 'convert'])->name('excel-to-pdf.convert');
    Route::get('/excel-import/{id?}', [ExcelImportController::class, 'view'])->name('excel.view');
    Route::post('/excel-import/api/{importId?}', [ExcelImportController::class, 'prescriptionListApi'])->name('prescription-list.api');

    // Staff bulk import routes
    Route::get('/staff-bulk-import', [ExcelImportController::class, 'staffBulkImport'])->name('excel.staff-bulk-import')->middleware('admin_or_operator');
    Route::post('/staff-bulk-import', [ExcelImportController::class, 'staffBulkImportStore'])->name('excel.staff-bulk-import.store');
    Route::post('/staff-bulk-import/update-preview-data', [ExcelImportController::class, 'updatePreviewData'])->name('excel.staff-bulk-import.update-preview-data');
    Route::post('/staff-bulk-import/process', [ExcelImportController::class, 'staffBulkImportProcess'])->name('excel.staff-bulk-import.process');
    Route::get('/staff-bulk-import/view/{id}', [ExcelImportController::class, 'staffBulkImportView'])->name('excel.staff-bulk-import.view');
    Route::get('/api/providers', [ExcelImportController::class, 'getProviders'])->name('api.providers');

    Route::get('/dashboard', 'Admin\HomeController@index')->name('dashboard');


    // Route::group(['prefix' => 'imports', 'as' => 'imports.'], function () {
    //     Route::get('/', 'Admin\ImportController@index')->name('index');
    //     Route::post('/api', 'Admin\ImportController@indexWeb')->name('api');
    //     Route::get('/api-pagination', 'Admin\ImportController@pagination')->name('api.pagination');
    // });

    Route::group(['middleware' => 'admin_or_operator', 'prefix' => 'users', 'as' => 'users.'], function () {
        Route::get('/', 'Admin\UserController@index')->name('index');
        Route::get('/create', 'Admin\UserController@create')->name('create');
        Route::get('/{user}/edit', 'Admin\UserController@edit')->name('edit');
        Route::post('/{user}/delete', 'Admin\UserController@delete')->name('delete');
        Route::post('/api', 'Admin\UserController@indexWeb')->name('api');
        Route::get('/api-pagination', 'Admin\UserController@pagination')->name('api.pagination');
        Route::get('/{user}/statusChange', 'Admin\UserController@change_status')->name('status');
        Route::post('/send-temp-password', 'Admin\UserController@sendTempPassword')->name('send-temp-password');
    });

    Route::group(['prefix' => 'admin', 'as' => 'admin.', 'middleware' => ['auth', 'admin']], function () {
        Route::get('/', 'Admin\AdminController@index')->name('index');
        Route::get('/create', 'Admin\AdminController@create')->name('create');
        Route::get('/{user}/edit', 'Admin\AdminController@edit')->name('edit');
        Route::post('/{user}/delete', 'Admin\AdminController@delete')->name('delete');
        Route::post('/api', 'Admin\AdminController@indexWeb')->name('api');
        Route::get('/api-pagination', 'Admin\AdminController@pagination')->name('api.pagination');
        Route::get('/{user}/statusChange', 'Admin\AdminController@change_status')->name('status');
    });

    // SETTINGS
    Route::group(['prefix' => 'settings', 'as' => 'settings.'], function () {
        Route::get('change-password', 'Admin\SettingController@change_password')->name('change-password');

        // Fax and Logs (Admin only)
        Route::group(['middleware' => 'admin'], function () {
            Route::get('fax-options', 'Admin\SettingController@faxOptions')->name('fax-options');

            Route::get('logs', 'Admin\LogController@index')->name('logs');
            Route::match(['get', 'post'], 'logs/api', 'Admin\LogController@indexWeb')->name('logs.api');
            Route::get('logs/types', 'Admin\LogController@getLogTypes')->name('logs.types');
            Route::get('logs/user-types', 'Admin\LogController@getUserTypes')->name('logs.user-types');
        });
        Route::get('/queues', 'Admin\ScriptController@queues')->name('queues');

        Route::group(['prefix' => 'terms-and-conditions', 'as' => 'terms.'], function () {
            Route::get('/', 'Admin\SettingController@terms_and_conditions')->name('index');
        });

        Route::group(['prefix' => 'privacy-policy', 'as' => 'privacy.'], function () {
            Route::get('/', 'Admin\SettingController@privacy_policy')->name('index');
        });

        Route::group(['prefix' => 'about-us', 'as' => 'about.'], function () {
            Route::get('/', 'Admin\SettingController@aboutUs')->name('index');
        });
    });

    // SCRIPTS
    Route::group(['prefix' => 'scripts', 'as' => 'scripts.'], function () {
        Route::prefix('template')->middleware(['admin'])->group(function () {
            Route::get('/', 'Admin\ScriptTemplateController@listTemplate')->name('template');
            Route::get('/add', 'Admin\ScriptTemplateController@addTemplate')->name('template.create');
            Route::post('/api/all', 'Admin\ScriptTemplateController@indexWebAll')->name('template.api.all');
            Route::post('/delete/{script_template}', 'Admin\ScriptTemplateController@delete')->name('template.delete');
            Route::get('/edit/{script_template}', 'Admin\ScriptTemplateController@editTemplate')->name('template.edit');
        });


        Route::get('/all', 'Admin\ScriptController@all')->name('all')->middleware('admin');
        Route::get('/ready-to-sign', 'Admin\ScriptController@readyToSign')->name('ready-to-sign');
        Route::get('/{importFile}/edit', 'Admin\ScriptController@edit')->name('edit')->middleware('provider');
        Route::get('/ready-to-send/approval', 'Admin\ScriptController@readyToSend')->name('ready-to-send')->middleware('admin_or_operator');
        Route::get('/ready-to-send', 'Admin\ScriptController@pendingApproval')->name('pending-approval');
        Route::get('/sent', 'Admin\ScriptController@sent')->name('sent');
        Route::get('/webhook/{id}', 'Admin\ScriptController@webhook')->name('webhook');
        Route::get('/provider-pending-approval', 'Admin\ScriptController@providerPendingApproval')->name('provider-pending-approval')->middleware('provider');
        Route::post('webhook/api/{id}', 'Admin\ScriptController@webhookAll')->name('webhook.api');
        Route::post('webhook/view/{webhook}', 'Admin\ScriptController@webhookView')->name('webhook.view');

        Route::get('view/{importId?}', 'Admin\ScriptController@preview')->name('preview');

        Route::post('/api/all', 'Admin\ScriptController@indexWebAll')->name('api.all');
        Route::post('/api/ready-to-sign', 'Admin\ScriptController@indexWebReadyToSign')->name('api.ready-to-sign');
        Route::post('/api/ready-to-send', 'Admin\ScriptController@indexWebReadyToSend')->name('api.ready-to-send');
        Route::post('/api/pending-approval', 'Admin\ScriptController@indexPendingApproval')->name('api.pending-approval');
        Route::post('/api/sent', 'Admin\ScriptController@indexSent')->name('api.sent');
        Route::post('/api/provider-pending-approval', 'Admin\ScriptController@indexProviderPendingApproval')->name('api.provider-pending-approval');
        Route::post('/api/return-script', 'Admin\ScriptController@returnScript')->name('api.return-script');
        Route::post('/api/void', 'Admin\ScriptController@void')->name('api.void');
        Route::get('/voided', 'Admin\ScriptController@voided')->name('voided');
        Route::post('/api/voided', 'Admin\ScriptController@indexVoided')->name('api.voided');


        Route::match(['get', 'post'], '/api/download-all-pdf/{importId?}', 'Admin\ScriptController@downloadAllPdf')->name('download-all-pdf');
        Route::post('/sign-all', 'Admin\ScriptController@signAll')->name('sign-pdf');
        Route::post('/send-all', 'Admin\ScriptController@sendAllForApproval')->name('send-for-approval');
        Route::post('send-fax', 'Admin\ScriptController@sendFaxAll')->name('send-fax');
        Route::get('/{importFile}/show-pdf', 'Admin\ScriptController@showPdf')->name('show-pdf');
        Route::post('/delete/{importFile}', 'Admin\ScriptController@delete')->name('delete');
    });

    Route::group(['prefix' => 'archive', 'as' => 'archive.'], function () {
        Route::get('/', 'ArchiveController@index')->name('index')->middleware('provider');
        Route::get('/show-pdf/{id?}', 'ArchiveController@show')->name('show-pdf');
        Route::post('/api', 'ArchiveController@indexWeb')->name('api');
        Route::match(['get', 'post'], '/download-all-pdf/{importId?}', 'ArchiveController@downloadAllPdf')->name('download-all-pdf');
        Route::get('/download/{id?}', 'ArchiveController@download')->name('file-download');
        Route::get('/show-all-pdf/{importId?}', 'ArchiveController@preview')->name('show-all-pdf')->middleware('provider');
        Route::get('/sign/{id?}', 'ArchiveController@sign')->name('sign-pdf')->middleware('provider');
        Route::get('send-fax/{id?}', 'ArchiveController@sendFax')->name('send-fax');
    });

    Route::group(['middleware' => ['admin_or_operator'], 'prefix' => 'archive_staff', 'as' => 'archive_staff.'], function () {
        Route::get('/', 'ArchiveController@StaffIndex')->name('index');
        Route::post('/api', 'ArchiveController@staffIndexWeb')->name('api');
        Route::get('/show-all-pdf/{importId?}', 'ArchiveController@staffPreview')->name('show-all-pdf');
        Route::get('/show-pdf/{id?}', 'ArchiveController@show')->name('show-pdf');
    });

    Route::group(['middleware' => ['admin_or_operator'], 'prefix' => 'medications', 'as' => 'medications.'], function () {
        Route::get('/', 'Admin\MedicationController@index')->name('index');
        Route::get('/create', 'Admin\MedicationController@create')->name('create');
        Route::post('/api', 'Admin\MedicationController@indexWeb')->name('api');
        Route::get('{medication}/edit', 'Admin\MedicationController@edit')->name('edit');
        Route::get('/{medication}/statusChange', 'Admin\MedicationController@change_status')->name('status');
        Route::post('/{medication}/delete', 'Admin\MedicationController@delete')->name('delete');
    });

    // Route::post('/dispensePro', 'Admin\ScriptController@SendDispenseJob')->name('send-dispense-pro');

    Route::get('/dispensePro', 'Admin\ScriptController@sendFaxAll')->name('send-dispense-pro');

    Route::group(['middleware' => 'auth', 'prefix' => 'patients', 'as' => 'patients.'], function () {
        Route::get('/', 'PatientController@index')->name('index');
        Route::post('/api', 'PatientController@indexWeb')->name('api');
        Route::get('/create', 'PatientController@create')->name('create');
        Route::get('{patient}/edit', 'PatientController@edit')->name('edit');
        Route::post('{patient}/delete', 'PatientController@delete')->name('delete');
        Route::get('{patient}/scripts', 'PatientController@PatientScripts')->name('scripts');
        Route::post('/scripts/api', 'PatientController@PatientScriptsApi')->name('scripts.api');
    });

    Route::group(['middleware' => 'auth', 'prefix' => 'practices', 'as' => 'practices.'], function () {
        Route::get('/', 'PracticeController@index')->name('index');
        Route::post('/api', 'PracticeController@indexWeb')->name('api');
        Route::post('/tokens', 'PracticeController@tokens')->name('tokens');
        Route::post('/tokens/api', 'PracticeController@indexWebTokens')->name('api.tokens');
        Route::post('/generate-token/{practice}', 'PracticeController@generateToken')->name('generate-token');
        Route::get('/create', 'PracticeController@create')->name('create');
        Route::get('{practice}/edit', 'PracticeController@edit')->name('edit');
        Route::get('{practice}/status-change', 'PracticeController@change_status')->name('status-change');
        Route::post('{practice}/delete', 'PracticeController@delete')->name('delete');
        Route::post('{practice}/delete-token/{tokenId}', 'PracticeController@deleteToken')->name('delete-token');

        Route::get('/{practice}/providers', 'PracticeController@providers')->name('providers');
        Route::post('/{practice}/api/providers', 'PracticeController@indexWebProviders')->name('providers.api');
        Route::get('/{practice}/providers/create', 'PracticeController@createProvider')->name('providers.create');
        Route::post('{practice}/providers/{provider}/delete', 'PracticeController@deleteProvider')->name('providers.delete');

        // Route::post('{practice}/delete', 'PracticeController@delete')->name('delete');
        Route::get('{practice}/medications', 'PracticeController@medications')->name('medications');
        Route::post('{practice}/api/medications', 'PracticeController@indexWebMedications')->name('api.medications');
        Route::get('{practice}/medications/create', 'PracticeController@createMedication')->name('medications.create');
        Route::get('{practice}/medications/{medication}/edit', 'PracticeController@editMedication')->name('medications.edit');
        Route::post('{practice}/medications/{medication}/delete', 'PracticeController@deleteMedication')->name('medications.delete');
    }); 
});
