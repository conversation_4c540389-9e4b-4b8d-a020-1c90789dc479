<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag; ?>
<?php foreach($attributes->onlyProps([
    'class' => '',
]) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
} ?>
<?php $attributes = $attributes->exceptProps([
    'class' => '',
]); ?>
<?php foreach (array_filter(([
    'class' => '',
]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
} ?>
<?php $__defined_vars = get_defined_vars(); ?>
<?php foreach ($attributes as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
} ?>
<?php unset($__defined_vars); ?>

<div class="card card-custom shadow-sm mb-8 <?php echo e($class); ?>" <?php echo e($attributes); ?>>

    <?php echo e($slot); ?>


</div>
<?php /**PATH C:\KodeCreators\newlife-panel\resources\views/components/card.blade.php ENDPATH**/ ?>