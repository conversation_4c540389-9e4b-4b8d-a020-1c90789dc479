<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag; ?>
<?php foreach($attributes->onlyProps([
    'fullWidth' => false,
    'class' => 'col-12 col-sm-6 col-lg-12 col-xl-6',
]) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
} ?>
<?php $attributes = $attributes->exceptProps([
    'fullWidth' => false,
    'class' => 'col-12 col-sm-6 col-lg-12 col-xl-6',
]); ?>
<?php foreach (array_filter(([
    'fullWidth' => false,
    'class' => 'col-12 col-sm-6 col-lg-12 col-xl-6',
]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
} ?>
<?php $__defined_vars = get_defined_vars(); ?>
<?php foreach ($attributes as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
} ?>
<?php unset($__defined_vars); ?>

<div class='<?php echo e($fullWidth ? 'col-12' : $class); ?>' <?php echo e($attributes); ?>>
    <?php echo e($slot); ?>

</div>
<?php /**PATH C:\KodeCreators\newlife-panel\resources\views/components/layout/col.blade.php ENDPATH**/ ?>