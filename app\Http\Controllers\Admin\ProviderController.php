<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;

class ProviderController extends Controller
{
    /**
     * Show the form for creating a new provider.
     */
    public function create()
    {
        $has_back = route('users.index');
        $page_title = 'Create New Provider';

        $livewire_component = 'provider.provider-form';
        $livewire_data = [];

        return view('layouts.livewire', [
            'page_title' => $page_title,
            'has_back' => $has_back,
            'livewire_component' => $livewire_component,
            'livewire_data' => $livewire_data,
        ]);
    }

    /**
     * Show the form for editing the specified provider.
     */
    public function edit(User $provider)
    {
        $has_back = route('users.index');
        $page_title = 'Edit Provider';

        $livewire_component = 'provider.provider-form';
        $livewire_data = [
            'provider' => $provider,
            'editMode' => true,
        ];

        return view('layouts.livewire', [
            'page_title' => $page_title,
            'has_back' => $has_back,
            'livewire_component' => $livewire_component,
            'livewire_data' => $livewire_data,
        ]);
    }
}
