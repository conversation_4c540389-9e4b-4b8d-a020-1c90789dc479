<?php

namespace App\Http\Livewire;

use Livewire\Component;
use Livewire\WithFileUploads;

class ProviderForm extends Component
{
    use WithFileUploads;

    public $step = 1;

    // Form fields
    public $email, $first_name, $last_name, $printed_name, $clinic_name;
    public $npi, $lic, $dea;
    public $phone, $fax, $dispatch_method, $address, $city, $state, $zip;
    public $signature;

    public function render()
    {
        return view('livewire.provider.provider-form');
    }

    public function nextStep()
    {
        $this->step++;
    }

    public function prevStep()
    {
        $this->step--;
    }

    public function submit()
    {
        $this->validate([
            'email' => 'required|email',
            'first_name' => 'required',
            'last_name' => 'required',
            'printed_name' => 'required',
            'npi' => 'required',
            'dispatch_method' => 'required',
            'signature' => 'required|file|max:2048|mimes:jpg,png,gif,webp,bmp',
        ]);

        // Save logic here...

        session()->flash('success', 'Provider created successfully!');
        return redirect()->route('users.index');
    }
}
