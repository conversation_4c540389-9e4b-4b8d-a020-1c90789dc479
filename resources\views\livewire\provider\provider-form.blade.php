<div>
    @if($step === 1)
        @include('livewire.provider.provider-steps.step1')
    @elseif($step === 2)
        @include('livewire.provider.provider-steps.step2')
    @elseif($step === 3)
        @include('livewire.provider.provider-steps.step3')
    @elseif($step === 4)
        @include('livewire.provider.provider-steps.step4')
    @endif

    <div class="mt-4 flex justify-between">
        @if($step > 1)
            <button wire:click="prevStep">Previous</button>
        @endif

        @if($step < 4)
            <button wire:click="nextStep">Next</button>
        @else
            <button wire:click="submit">Submit</button>
        @endif
    </div>
</div>
