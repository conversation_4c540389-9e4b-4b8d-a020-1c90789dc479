<?php

namespace App\Http\Livewire\Shared;

use Livewire\Component;

class MultiStepForm extends Component
{
    public $steps = [];
    public $currentStep = 0;
    public $formData = [];

    public $showScriptTable = false;

    public function mount($steps)
    {
        $this->steps = $steps;
    }

    public function next()
    {
        if ($this->currentStep < count($this->steps) - 1) {
            $this->currentStep++;
        }
    }

    public function back()
    {
        if ($this->currentStep > 0) {
            $this->currentStep--;
        }
    }

    public function render()
    {
        return view('livewire.shared.multi-step-form');
    }
}
