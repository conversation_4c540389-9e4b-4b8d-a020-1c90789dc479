<?php

namespace App\Http\Livewire\Provider;

use App\Models\User;
use App\Models\State;
use Livewire\Component;
use Livewire\WithFileUploads;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;

class ProviderForm extends Component
{
    use WithFileUploads;

    public $step = 1;
    public $provider;
    public $editMode = false;

    // Form fields
    public $email, $first_name, $last_name, $printed_name, $clinic_name;
    public $npi, $lic, $dea;
    public $phone, $fax, $dispatch_method, $address, $city, $state, $zip;
    public $signature;
    public $password, $password_confirmation;

    public $states = [];

    public function mount($provider = null, $editMode = false)
    {
        $this->editMode = $editMode;
        $this->loadStates();

        if ($this->editMode && $provider) {
            $this->provider = $provider;
            $this->loadProviderData();
        }
    }

    public function loadStates()
    {
        $this->states = State::orderBy('name')->get();
    }

    public function loadProviderData()
    {
        $this->email = $this->provider->email;
        $this->first_name = $this->provider->first_name;
        $this->last_name = $this->provider->last_name;
        $this->printed_name = $this->provider->printed_name;
        $this->clinic_name = $this->provider->clinic_name;
        $this->npi = $this->provider->{'NPI#'};
        $this->lic = $this->provider->{'LIC#'};
        $this->dea = $this->provider->{'DEA#'};
        $this->phone = $this->provider->phone;
        $this->fax = $this->provider->fax;
        $this->dispatch_method = $this->provider->dispatch_method;
        $this->address = $this->provider->address;
        $this->city = $this->provider->city;
        $this->state = $this->provider->state;
        $this->zip = $this->provider->zip;
    }

    public function render()
    {
        return view('livewire.provider.provider-form');
    }

    public function nextStep()
    {
        $this->validateCurrentStep();
        $this->step++;
    }

    public function prevStep()
    {
        $this->step--;
    }

    public function goToStep($stepNumber)
    {
        // Only allow direct step navigation in edit mode
        if ($this->editMode && $stepNumber >= 1 && $stepNumber <= 4) {
            $this->step = $stepNumber;
        }
    }

    private function validateCurrentStep()
    {
        $rules = [];

        switch ($this->step) {
            case 1:
                $rules = [
                    'email' => 'required|email|unique:users,email' . ($this->editMode ? ',' . $this->provider->id : ''),
                    'first_name' => 'required|string|max:255',
                    'last_name' => 'required|string|max:255',
                    'printed_name' => 'required|string|max:255',
                    'clinic_name' => 'required|string|max:255',
                ];

                break;

            case 2:
                $rules = [
                    'npi' => 'required|digits:10|unique:users,NPI#' . ($this->editMode ? ',' . $this->provider->id : ''),
                    'lic' => 'nullable|string|max:255',
                    'dea' => 'nullable|string|max:255',
                ];
                break;

            case 3:
                $rules = [
                    'phone' => 'nullable|string|max:20',
                    'fax' => 'nullable|string|max:20',
                    'dispatch_method' => 'required|in:Email,Fax,Dispense Pro',
                    'address' => 'required|string|max:255',
                    'city' => 'required|string|max:255',
                    'state' => 'nullable|string|max:2',
                    'zip' => 'required|regex:/^\d{5}(-\d{4})?$/',
                ];
                break;
        }

        $this->validate($rules);
    }

    public function submit()
    {
        $this->validateCurrentStep();

        // Final validation for signature
        $signatureRules = [];
        if (!$this->editMode) {
            $signatureRules['signature'] = 'required|file|max:2048|mimes:jpg,png,gif,webp,bmp';
        } else {
            $signatureRules['signature'] = 'nullable|file|max:2048|mimes:jpg,png,gif,webp,bmp';
        }
        $this->validate($signatureRules);

        try {
            if ($this->editMode) {
                $this->updateProvider();
            } else {
                $this->createProvider();
            }

            session()->flash('success-message', $this->editMode ? 'Provider updated successfully!' : 'Provider created successfully!');
            return redirect()->route('users.index');
        } catch (\Exception $e) {
            Log::error('Provider save error: ' . $e->getMessage());
            session()->flash('error-message', 'An error occurred while saving the provider. Please try again.');
        }
    }

    private function createProvider()
    {
        $signaturePath = null;
        if ($this->signature) {
            $signaturePath = $this->signature->store('signatures', 'public');
        }

        User::create([
            'email' => $this->email,
            'first_name' => $this->first_name,
            'last_name' => $this->last_name,
            'printed_name' => $this->printed_name,
            'clinic_name' => $this->clinic_name,
            'NPI#' => $this->npi,
            'LIC#' => $this->lic,
            'DEA#' => $this->dea,
            'phone' => $this->phone,
            'fax' => $this->fax,
            'dispatch_method' => $this->dispatch_method,
            'address' => $this->address,
            'city' => $this->city,
            'state' => $this->state,
            'zip' => $this->zip,
            'signature' => $signaturePath,
            'password' => Hash::make($this->password),
            'role' => User::ROLE_PROVIDER,
            'is_active' => 1,
        ]);
    }

    private function updateProvider()
    {
        $updateData = [
            'email' => $this->email,
            'first_name' => $this->first_name,
            'last_name' => $this->last_name,
            'printed_name' => $this->printed_name,
            'clinic_name' => $this->clinic_name,
            'NPI#' => $this->npi,
            'LIC#' => $this->lic,
            'DEA#' => $this->dea,
            'phone' => $this->phone,
            'fax' => $this->fax,
            'dispatch_method' => $this->dispatch_method,
            'address' => $this->address,
            'city' => $this->city,
            'state' => $this->state,
            'zip' => $this->zip,
        ];

        if ($this->signature) {
            // Delete old signature if exists
            if ($this->provider->signature) {
                Storage::disk('public')->delete($this->provider->signature);
            }
            $updateData['signature'] = $this->signature->store('signatures', 'public');
        }

        $this->provider->update($updateData);
    }
}
