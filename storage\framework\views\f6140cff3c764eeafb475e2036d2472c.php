<div>
    <?php if (isset($component)) { $__componentOriginal3c8f9b7880ed8037e1c74e30535cdc4c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal3c8f9b7880ed8037e1c74e30535cdc4c = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layout.row','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('layout.row'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
        <div class="col">
            <?php if (isset($component)) { $__componentOriginalcc4d6dcf44f2ce2176eee5939e2828e6 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalcc4d6dcf44f2ce2176eee5939e2828e6 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.card.body','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('card.body'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
                <!-- Step Progress Indicator -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="progress" style="height: 8px;">
                            <div class="progress-bar bg-primary" role="progressbar" style="width: <?php echo e(($step / 4) * 100); ?>%"></div>
                        </div>
                        <div class="d-flex justify-content-between mt-2">
                            <small class="text-muted">Step <?php echo e($step); ?> of 4</small>
                            <small class="text-muted">
                                <?php if($step === 1): ?> Basic Information
                                <?php elseif($step === 2): ?> Professional Details
                                <?php elseif($step === 3): ?> Contact & Address
                                <?php elseif($step === 4): ?> Signature
                                <?php endif; ?>
                            </small>
                        </div>
                    </div>
                </div>

                <!-- Step Content -->
                <?php if($step === 1): ?>
                    <?php echo $__env->make('livewire.provider.provider-steps.step1', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                <?php elseif($step === 2): ?>
                    <?php echo $__env->make('livewire.provider.provider-steps.step2', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                <?php elseif($step === 3): ?>
                    <?php echo $__env->make('livewire.provider.provider-steps.step3', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                <?php elseif($step === 4): ?>
                    <?php echo $__env->make('livewire.provider.provider-steps.step4', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                <?php endif; ?>
             <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalcc4d6dcf44f2ce2176eee5939e2828e6)): ?>
<?php $attributes = $__attributesOriginalcc4d6dcf44f2ce2176eee5939e2828e6; ?>
<?php unset($__attributesOriginalcc4d6dcf44f2ce2176eee5939e2828e6); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalcc4d6dcf44f2ce2176eee5939e2828e6)): ?>
<?php $component = $__componentOriginalcc4d6dcf44f2ce2176eee5939e2828e6; ?>
<?php unset($__componentOriginalcc4d6dcf44f2ce2176eee5939e2828e6); ?>
<?php endif; ?>

            <?php if (isset($component)) { $__componentOriginal2f4c20c75f25d521b59e2ab32e77183d = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal2f4c20c75f25d521b59e2ab32e77183d = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.group.errors','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('group.errors'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal2f4c20c75f25d521b59e2ab32e77183d)): ?>
<?php $attributes = $__attributesOriginal2f4c20c75f25d521b59e2ab32e77183d; ?>
<?php unset($__attributesOriginal2f4c20c75f25d521b59e2ab32e77183d); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal2f4c20c75f25d521b59e2ab32e77183d)): ?>
<?php $component = $__componentOriginal2f4c20c75f25d521b59e2ab32e77183d; ?>
<?php unset($__componentOriginal2f4c20c75f25d521b59e2ab32e77183d); ?>
<?php endif; ?>

            <?php if (isset($component)) { $__componentOriginal7274efa5ddca416a8c7d5935f6f9a8c0 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal7274efa5ddca416a8c7d5935f6f9a8c0 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.card.footer','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('card.footer'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
                <div class="d-flex justify-content-between">
                    <?php if($step > 1): ?>
                        <button type="button" class="btn btn-secondary" wire:click="prevStep" wire:loading.attr="disabled">
                            <i class="fas fa-arrow-left me-1"></i> Previous
                        </button>
                    <?php else: ?>
                        <div></div>
                    <?php endif; ?>

                    <?php if($step < 4): ?>
                        <button type="button" class="btn btn-primary" wire:click="nextStep" wire:loading.attr="disabled">
                            Next <i class="fas fa-arrow-right ms-1"></i>
                        </button>
                    <?php else: ?>
                        <button type="button" class="btn btn-success" wire:click="submit" wire:loading.attr="disabled" wire:loading.class="disabled">
                            <span wire:loading.remove wire:target="submit">
                                <i class="fas fa-save me-1"></i> <?php echo e($editMode ? 'Update Provider' : 'Create Provider'); ?>

                            </span>
                            <span wire:loading wire:target="submit">
                                <i class="fas fa-spinner fa-spin me-1"></i> <?php echo e($editMode ? 'Updating...' : 'Creating...'); ?>

                            </span>
                        </button>
                    <?php endif; ?>
                </div>
             <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal7274efa5ddca416a8c7d5935f6f9a8c0)): ?>
<?php $attributes = $__attributesOriginal7274efa5ddca416a8c7d5935f6f9a8c0; ?>
<?php unset($__attributesOriginal7274efa5ddca416a8c7d5935f6f9a8c0); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal7274efa5ddca416a8c7d5935f6f9a8c0)): ?>
<?php $component = $__componentOriginal7274efa5ddca416a8c7d5935f6f9a8c0; ?>
<?php unset($__componentOriginal7274efa5ddca416a8c7d5935f6f9a8c0); ?>
<?php endif; ?>
        </div>
     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal3c8f9b7880ed8037e1c74e30535cdc4c)): ?>
<?php $attributes = $__attributesOriginal3c8f9b7880ed8037e1c74e30535cdc4c; ?>
<?php unset($__attributesOriginal3c8f9b7880ed8037e1c74e30535cdc4c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal3c8f9b7880ed8037e1c74e30535cdc4c)): ?>
<?php $component = $__componentOriginal3c8f9b7880ed8037e1c74e30535cdc4c; ?>
<?php unset($__componentOriginal3c8f9b7880ed8037e1c74e30535cdc4c); ?>
<?php endif; ?>
</div>
<?php /**PATH C:\KodeCreators\newlife-panel\resources\views/livewire/provider/provider-form.blade.php ENDPATH**/ ?>